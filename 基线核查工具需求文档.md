# 基线核查工具需求文档

## 1. 项目概述

### 1.1 项目名称
基线核查工具 (Baseline Security Checker)

### 1.2 项目描述
基于Go Wails框架开发的跨平台桌面应用程序，用于检查系统安全配置是否符合基线要求，提供直观的图形界面和详细的检查报告。

### 1.3 技术栈
- **后端**: Go 1.19+
- **框架**: Wails v2
- **前端**: HTML/CSS/JavaScript (Vanilla JS)
- **数据库**: SQLite
- **打包**: 原生二进制文件

### 1.4 目标平台
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 20.04+)

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 系统信息收集模块
**功能描述**: 自动收集系统基础信息
**具体功能**:
- 操作系统版本和架构信息
- 硬件配置信息（CPU、内存、磁盘）
- 网络配置信息（IP地址、网关、DNS）
- 当前用户和权限信息

#### 2.1.2 基线检查模块
**功能描述**: 根据预定义规则检查系统安全配置
**检查项目**:

**用户账户安全**:
- 管理员账户数量检查
- 默认账户状态检查
- 密码策略检查（复杂度、有效期）
- 用户权限分配检查

**系统服务检查**:
- 危险服务状态检查
- 不必要服务运行检查
- 服务启动类型检查
- 端口开放状态检查

**文件系统安全**:
- 关键目录权限检查
- 系统文件完整性检查
- 临时文件清理检查
- 日志文件配置检查

**网络安全配置**:
- 防火墙状态检查
- 网络共享配置检查
- 远程访问配置检查
- 网络协议安全检查

#### 2.1.3 规则管理模块
**功能描述**: 管理检查规则和基线标准
**具体功能**:
- 预置常见安全基线（等保2.0、CIS基线）
- 自定义检查规则配置
- 规则导入导出功能
- 规则优先级设置

#### 2.1.4 报告生成模块
**功能描述**: 生成详细的检查报告
**具体功能**:
- 实时检查结果展示
- 风险等级分类（高、中、低）
- HTML格式报告导出
- PDF格式报告导出
- 历史检查记录管理

#### 2.1.5 配置管理模块
**功能描述**: 应用程序配置和用户偏好设置
**具体功能**:
- 检查项目启用/禁用配置
- 检查频率设置
- 报告模板自定义
- 界面主题设置

### 2.2 用户界面设计

#### 2.2.1 主界面
- **仪表板**: 显示系统概览和最近检查结果
- **快速检查按钮**: 一键启动全面检查
- **检查进度显示**: 实时显示检查进度和状态

#### 2.2.2 检查结果界面
- **结果概览**: 通过/失败项目统计
- **详细列表**: 分类显示检查项目和结果
- **风险评估**: 可视化风险等级分布
- **修复建议**: 针对失败项目提供修复指导

#### 2.2.3 配置界面
- **规则配置**: 检查规则的启用/禁用设置
- **基线选择**: 选择适用的安全基线标准
- **报告设置**: 配置报告格式和内容

#### 2.2.4 历史记录界面
- **检查历史**: 历史检查记录列表
- **趋势分析**: 安全状况变化趋势图表
- **报告管理**: 历史报告查看和导出

## 3. 非功能需求

### 3.1 性能要求
- 应用启动时间 < 3秒
- 完整系统检查时间 < 5分钟
- 内存占用 < 100MB
- 支持并发检查项目执行

### 3.2 可用性要求
- 界面简洁直观，无需培训即可使用
- 支持中英文界面切换
- 提供详细的帮助文档
- 错误信息清晰易懂

### 3.3 安全要求
- 本地数据加密存储
- 不收集用户隐私信息
- 检查过程不修改系统配置
- 支持只读模式运行

### 3.4 兼容性要求
- 支持主流操作系统版本
- 向后兼容旧版本配置文件
- 支持不同架构（x64、ARM64）

## 4. 技术架构设计

### 4.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Go后端服务    │
│  (HTML/CSS/JS)  │◄──►│   (Wails App)   │
└─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   数据存储      │
                       │   (SQLite)      │
                       └─────────────────┘
```

### 4.2 核心模块设计

#### 4.2.1 检查引擎 (CheckEngine)
- 规则解析器
- 检查执行器
- 结果收集器
- 并发控制器

#### 4.2.2 系统接口 (SystemInterface)
- Windows API调用
- Linux系统调用
- macOS系统调用
- 跨平台抽象层

#### 4.2.3 数据管理 (DataManager)
- SQLite数据库操作
- 配置文件管理
- 缓存管理
- 数据导入导出

#### 4.2.4 报告生成器 (ReportGenerator)
- HTML模板引擎
- PDF生成器
- 图表生成器
- 数据可视化

## 5. 数据库设计

### 5.1 主要数据表

#### 5.1.1 检查规则表 (check_rules)
```sql
CREATE TABLE check_rules (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    severity TEXT,
    enabled BOOLEAN DEFAULT 1,
    rule_content TEXT,
    created_at DATETIME,
    updated_at DATETIME
);
```

#### 5.1.2 检查结果表 (check_results)
```sql
CREATE TABLE check_results (
    id INTEGER PRIMARY KEY,
    session_id TEXT NOT NULL,
    rule_id INTEGER,
    status TEXT NOT NULL,
    message TEXT,
    details TEXT,
    checked_at DATETIME,
    FOREIGN KEY (rule_id) REFERENCES check_rules(id)
);
```

#### 5.1.3 检查会话表 (check_sessions)
```sql
CREATE TABLE check_sessions (
    id TEXT PRIMARY KEY,
    start_time DATETIME,
    end_time DATETIME,
    total_checks INTEGER,
    passed_checks INTEGER,
    failed_checks INTEGER,
    system_info TEXT
);
```

## 6. 开发计划

### 6.1 开发阶段

#### 第一阶段 (2周)
- 项目初始化和基础架构搭建
- Wails项目创建和配置
- 基础UI界面设计
- 数据库设计和创建

#### 第二阶段 (3周)
- 系统信息收集模块开发
- 基础检查规则实现
- 检查引擎核心功能开发
- 基本UI交互实现

#### 第三阶段 (2周)
- 报告生成功能开发
- 配置管理功能实现
- 规则管理界面开发
- 错误处理和日志记录

#### 第四阶段 (1周)
- 跨平台测试和优化
- 性能调优
- 用户界面优化
- 文档编写

### 6.2 交付物
- 可执行程序（Windows/macOS/Linux）
- 用户使用手册
- 技术文档
- 源代码和构建脚本

## 7. 风险评估

### 7.1 技术风险
- **跨平台兼容性**: 不同操作系统API差异
- **性能问题**: 大量检查项目可能影响性能
- **权限问题**: 某些检查需要管理员权限

### 7.2 缓解措施
- 充分的跨平台测试
- 异步执行和进度反馈
- 权限检查和友好提示
- 模块化设计便于维护

## 8. 总结

本基线核查工具设计简洁实用，功能覆盖主要安全检查需求，采用现代化的技术栈确保良好的用户体验和跨平台兼容性。通过模块化设计和清晰的架构，便于后续功能扩展和维护。
