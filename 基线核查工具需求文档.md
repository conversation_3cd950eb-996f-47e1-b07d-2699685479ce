# SSH远程基线核查工具需求文档

## 1. 项目概述

### 1.1 项目名称
SSH远程基线核查工具 (SSH Security Baseline Checker)

### 1.2 项目描述
一款轻量级的跨平台桌面应用程序，专注于通过SSH协议远程执行服务器安全基线检查。采用简洁的操作流程和直观的结果展示，帮助IT管理员快速发现和解决服务器安全配置问题。

### 1.3 产品定位
- **目标用户**: 中小企业IT管理员、运维工程师、安全审计人员
- **核心价值**: 快速、简单、可靠的远程安全检查
- **使用场景**: 定期安全检查、合规性审计、新服务器上线验证

### 1.4 技术栈
- **后端**: Go 1.19+
- **框架**: Wails v2
- **前端**: HTML/CSS/JavaScript + 轻量级UI框架
- **数据库**: SQLite (本地存储)
- **SSH库**: golang.org/x/crypto/ssh
- **打包**: 单一可执行文件

### 1.5 支持平台
**管理端**:
- Windows 10/11 (x64)
- macOS 10.15+ (Intel/Apple Silicon)
- Linux (Ubuntu 20.04+, CentOS 8+)

**目标服务器**:
- Linux系统 (CentOS 7+, Ubuntu 18.04+, RHEL 7+, Debian 10+)
- 支持SSH连接的Unix系统

## 2. 核心功能设计

### 2.1 设计原则
- **简单易用**: 3步完成检查（添加服务器 → 选择检查项 → 执行检查）
- **结果清晰**: 红绿灯式状态显示，问题优先级明确
- **批量高效**: 支持批量操作，提高工作效率
- **安全可靠**: SSH加密传输，本地数据保护

### 2.2 功能模块

#### 2.2.1 服务器管理模块
**功能描述**: 简化的服务器连接管理
**核心功能**:
- **快速添加**: 向导式添加服务器（IP、端口、认证信息）
- **连接测试**: 一键测试SSH连接状态
- **批量导入**: 支持CSV格式批量导入服务器信息
- **分组管理**: 简单的标签分组（生产、测试、开发）
- **连接状态**: 实时显示连接状态和最后检查时间

#### 2.2.2 检查模板模块
**功能描述**: 预置和自定义检查项管理
**核心功能**:
- **预置模板**:
  - Linux基础安全检查（用户、权限、服务、网络）
  - CIS基线检查（简化版）
  - 等保2.0基础要求
- **自定义检查**: 支持添加Shell命令检查项
- **模板管理**: 启用/禁用检查项，设置检查优先级
- **检查说明**: 每个检查项包含说明和修复建议

#### 2.2.3 检查执行模块
**功能描述**: 远程执行安全检查并收集结果
**核心功能**:
- **单台检查**: 选择服务器和检查模板，执行检查
- **批量检查**: 同时对多台服务器执行相同检查
- **实时进度**: 显示检查进度和当前执行的检查项
- **并发控制**: 最多同时检查5台服务器（可配置）
- **错误处理**: 连接失败自动重试，记录错误信息

#### 2.2.4 结果展示模块
**功能描述**: 直观展示检查结果和问题分析
**核心功能**:
- **状态概览**: 红绿灯显示整体安全状态
- **问题分类**: 按严重程度分类（高危、中危、低危、信息）
- **详细结果**: 每个检查项的具体结果和说明
- **对比视图**: 多台服务器结果横向对比
- **修复建议**: 针对失败项目提供具体修复步骤

#### 2.2.5 报告导出模块
**功能描述**: 生成和管理检查报告
**核心功能**:
- **HTML报告**: 包含图表和详细信息的网页报告
- **PDF报告**: 适合打印和分享的PDF格式
- **Excel报告**: 便于数据分析的表格格式
- **历史记录**: 保存最近30次检查记录
- **报告模板**: 可自定义报告格式和内容

### 2.3 预置检查项目

#### 2.3.1 Linux基础安全检查
**用户账户安全**:
- Root账户状态检查
- 空密码账户检查
- 密码过期策略检查
- sudo权限配置检查

**系统服务安全**:
- 不必要服务检查（telnet、ftp等）
- SSH配置安全检查
- 防火墙状态检查
- 系统更新状态检查

**文件系统安全**:
- 关键目录权限检查（/etc、/var/log等）
- SUID/SGID文件检查
- 世界可写文件检查
- 日志文件权限检查

**网络安全**:
- 开放端口检查
- 网络服务配置检查
- IP转发配置检查
- 网络参数安全检查

#### 2.3.2 合规性检查模板
**等保2.0基础要求**:
- 身份鉴别
- 访问控制
- 安全审计
- 入侵防范

**CIS基线（简化版）**:
- 初始设置
- 服务配置
- 网络配置
- 日志和审计

## 3. 用户界面设计

### 3.1 界面设计原则
- **简洁明了**: 减少不必要的元素，突出核心功能
- **操作直观**: 符合用户习惯的操作流程
- **信息清晰**: 重要信息突出显示，状态一目了然
- **响应式**: 适配不同屏幕尺寸

### 3.2 主要界面

#### 3.2.1 主界面（仪表板）
```
┌─────────────────────────────────────────────────────────┐
│ SSH基线核查工具                    [设置] [帮助] [关于]  │
├─────────────────────────────────────────────────────────┤
│ 服务器概览                                              │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐        │
│ │ 总数: 12│ │ 在线: 10│ │ 正常: 8 │ │ 异常: 2 │        │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘        │
│                                                         │
│ 快速操作                                                │
│ [添加服务器] [批量检查] [查看报告] [检查模板]           │
│                                                         │
│ 最近检查结果                                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 服务器名称    状态    最后检查时间    问题数量      │ │
│ │ web-01       🟢正常   2024-01-15     0             │ │
│ │ db-01        🟡警告   2024-01-15     3             │ │
│ │ app-01       🔴异常   2024-01-14     8             │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 3.2.2 服务器管理界面
- **服务器列表**: 表格形式显示服务器信息
- **添加服务器**: 简化的表单（IP、端口、用户名、认证方式）
- **连接测试**: 一键测试按钮，显示连接状态
- **批量操作**: 选择多台服务器进行批量检查

#### 3.2.3 检查执行界面
- **检查模板选择**: 下拉选择预置模板
- **服务器选择**: 复选框选择目标服务器
- **执行进度**: 进度条显示整体进度，列表显示各服务器状态
- **实时日志**: 显示当前执行的检查项和结果

#### 3.2.4 结果查看界面
- **概览面板**: 饼图显示通过/失败/警告比例
- **问题列表**: 按严重程度分类显示问题
- **详细信息**: 点击问题查看详细描述和修复建议
- **服务器对比**: 表格形式对比多台服务器结果

## 4. 技术架构

### 4.1 整体架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端UI层      │    │   业务逻辑层    │    │   数据访问层    │
│  (HTML/CSS/JS)  │◄──►│   (Go Service)  │◄──►│   (SQLite)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   SSH客户端     │
                       │   (远程执行)    │
                       └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   目标服务器    │
                       │   (Linux系统)   │
                       └─────────────────┘
```

### 4.2 核心组件

#### 4.2.1 SSH连接管理器
- **连接池**: 复用SSH连接，提高性能
- **认证管理**: 支持密码和密钥认证
- **超时控制**: 连接和命令执行超时设置
- **错误重试**: 自动重试机制

#### 4.2.2 检查执行引擎
- **脚本管理**: 检查脚本的加载和执行
- **结果解析**: 解析命令输出，判断检查结果
- **并发控制**: 控制同时执行的检查数量
- **进度跟踪**: 实时更新检查进度

#### 4.2.3 数据管理层
- **服务器信息**: 存储服务器连接信息
- **检查结果**: 存储历史检查结果
- **配置管理**: 应用配置和用户偏好
- **数据加密**: 敏感信息加密存储

## 5. 非功能需求

### 5.1 性能要求
- 应用启动时间 < 3秒
- 单台服务器检查时间 < 3分钟
- 支持管理50台服务器
- 最多5个并发SSH连接
- 内存占用 < 100MB

### 5.2 可用性要求
- 零学习成本，直观易用
- 中文界面，符合国内用户习惯
- 详细的错误提示和解决建议
- 内置帮助文档

### 5.3 安全要求
- SSH连接信息本地加密存储
- 支持SSH密钥认证
- 只读检查，不修改系统
- 操作日志记录

### 5.4 兼容性要求
- 支持主流Linux发行版
- 兼容SSH 2.0协议
- 支持IPv4连接

## 6. 数据库设计

### 6.1 数据表结构

#### 6.1.1 服务器信息表 (servers)
```sql
CREATE TABLE servers (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,           -- 服务器名称
    host TEXT NOT NULL,           -- IP地址或域名
    port INTEGER DEFAULT 22,      -- SSH端口
    username TEXT NOT NULL,       -- 登录用户名
    auth_type TEXT NOT NULL,      -- 认证类型: password/key
    password TEXT,                -- 加密存储的密码
    key_path TEXT,               -- 私钥文件路径
    group_tag TEXT,              -- 分组标签
    description TEXT,            -- 描述信息
    status TEXT DEFAULT 'unknown', -- 连接状态
    last_check DATETIME,         -- 最后检查时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.1.2 检查模板表 (check_templates)
```sql
CREATE TABLE check_templates (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,          -- 模板名称
    description TEXT,            -- 模板描述
    category TEXT,               -- 分类: basic/cis/dengbao
    enabled BOOLEAN DEFAULT 1,   -- 是否启用
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.1.3 检查项目表 (check_items)
```sql
CREATE TABLE check_items (
    id INTEGER PRIMARY KEY,
    template_id INTEGER,         -- 所属模板
    name TEXT NOT NULL,          -- 检查项名称
    description TEXT,            -- 检查说明
    command TEXT NOT NULL,       -- 执行命令
    expected_result TEXT,        -- 期望结果
    severity TEXT DEFAULT 'medium', -- 严重程度: high/medium/low
    fix_suggestion TEXT,         -- 修复建议
    enabled BOOLEAN DEFAULT 1,
    FOREIGN KEY (template_id) REFERENCES check_templates(id)
);
```

#### 6.1.4 检查结果表 (check_results)
```sql
CREATE TABLE check_results (
    id INTEGER PRIMARY KEY,
    session_id TEXT NOT NULL,    -- 检查会话ID
    server_id INTEGER,           -- 服务器ID
    item_id INTEGER,            -- 检查项ID
    status TEXT NOT NULL,        -- 结果: pass/fail/error
    output TEXT,                -- 命令输出
    message TEXT,               -- 结果说明
    checked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES servers(id),
    FOREIGN KEY (item_id) REFERENCES check_items(id)
);
```

#### 6.1.5 检查会话表 (check_sessions)
```sql
CREATE TABLE check_sessions (
    id TEXT PRIMARY KEY,         -- 会话ID (UUID)
    name TEXT,                   -- 会话名称
    template_id INTEGER,         -- 使用的模板
    server_count INTEGER,        -- 检查服务器数量
    total_items INTEGER,         -- 总检查项数
    passed_items INTEGER,        -- 通过项数
    failed_items INTEGER,        -- 失败项数
    start_time DATETIME,         -- 开始时间
    end_time DATETIME,          -- 结束时间
    status TEXT DEFAULT 'running', -- 状态: running/completed/failed
    FOREIGN KEY (template_id) REFERENCES check_templates(id)
);
```

## 7. 开发计划

### 7.1 开发阶段 (总计6周)

#### 第一阶段 (1周) - 项目搭建
- **目标**: 完成基础项目架构
- **任务**:
  - Wails项目初始化和配置
  - 基础UI框架搭建
  - SQLite数据库设计和创建
  - SSH连接基础功能实现

#### 第二阶段 (2周) - 核心功能
- **目标**: 实现服务器管理和基础检查
- **任务**:
  - 服务器添加、编辑、删除功能
  - SSH连接测试和状态监控
  - 基础检查项目实现（10-15个）
  - 检查执行引擎开发

#### 第三阶段 (2周) - 检查和结果
- **目标**: 完善检查功能和结果展示
- **任务**:
  - 批量检查功能实现
  - 检查结果存储和展示
  - 预置检查模板开发
  - 错误处理和重试机制

#### 第四阶段 (1周) - 报告和优化
- **目标**: 完成报告功能和整体优化
- **任务**:
  - HTML/PDF报告生成
  - 历史记录管理
  - 性能优化和测试
  - 用户文档编写

### 7.2 交付物
- 跨平台可执行程序
- 用户操作手册
- 预置检查模板
- 源代码和构建脚本

## 8. 风险评估与应对

### 8.1 主要风险
- **网络连接**: SSH连接可能因网络问题中断
- **权限限制**: 部分检查需要root权限
- **系统兼容**: 不同Linux发行版命令差异
- **用户接受度**: 新工具的学习和接受成本

### 8.2 应对措施
- 实现自动重连和超时处理
- 提供权限检查和清晰的错误提示
- 针对主流发行版进行充分测试
- 设计简洁直观的用户界面

## 9. 项目总结

### 9.1 产品特色
这是一款**轻量级、易用性强**的SSH远程基线核查工具，具有以下特色：

- **操作简单**: 3步完成检查，无需复杂配置
- **结果清晰**: 红绿灯式状态显示，问题一目了然
- **批量高效**: 支持多台服务器同时检查
- **安全可靠**: 基于SSH协议，数据传输安全
- **本地部署**: 无需云服务，数据完全本地控制

### 9.2 适用场景
- **中小企业**: 服务器数量适中，需要定期安全检查
- **运维团队**: 提高日常安全检查效率
- **合规审计**: 快速生成合规性检查报告
- **安全评估**: 新服务器上线前的安全验证

### 9.3 核心价值
通过简化操作流程和直观的结果展示，让安全基线检查变得**简单、快速、可靠**，帮助用户及时发现和解决服务器安全问题，提升整体安全管理水平。

---

**项目代号**: SSH-Baseline-Checker
**预计开发周期**: 6周
**目标用户**: 中小企业IT管理员、运维工程师
**核心理念**: 简单、实用、可靠
