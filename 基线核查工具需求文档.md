# 基线核查工具需求文档

## 1. 项目概述

### 1.1 项目名称
基线核查工具 (Baseline Security Checker)

### 1.2 项目描述
基于Go Wails框架开发的跨平台桌面应用程序，通过SSH远程连接目标服务器，执行安全基线检查，提供集中化的安全管理和详细的检查报告。支持批量管理多台服务器的安全配置检查。

### 1.3 技术栈
- **后端**: Go 1.19+
- **框架**: Wails v2
- **前端**: HTML/CSS/JavaScript (Vanilla JS)
- **数据库**: SQLite
- **打包**: 原
生二进制文件

### 1.4 目标平台
**管理端（本地应用）**:
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 20.04+)

**被检查端（远程服务器）**:
- Linux服务器 (CentOS 7+, Ubuntu 18.04+, RHEL 7+)
- Windows Server 2016+
- 支持SSH连接的Unix/Linux系统

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 SSH连接管理模块
**功能描述**: 管理远程服务器连接和认证
**具体功能**:
- SSH连接配置和管理
- 支持密码和密钥认证
- 连接状态监控和重连机制
- 批量服务器管理
- 连接池管理优化性能

#### 2.1.2 系统信息收集模块
**功能描述**: 通过SSH远程收集目标系统信息
**具体功能**:
- 远程获取操作系统版本和架构信息
- 远程收集硬件配置信息（CPU、内存、磁盘）
- 远程获取网络配置信息（IP地址、网关、DNS）
- 远程检查当前用户和权限信息
- 支持多种Linux发行版和Windows Server

#### 2.1.3 远程基线检查模块
**功能描述**: 通过SSH远程执行安全基线检查
**检查项目**:

**用户账户安全**:
- 远程检查管理员账户数量和状态
- 远程验证默认账户配置
- 远程检查密码策略（复杂度、有效期）
- 远程审计用户权限分配
- 检查sudo配置和权限

**系统服务检查**:
- 远程检查危险服务运行状态
- 远程识别不必要的服务
- 远程验证服务启动配置
- 远程扫描开放端口状态
- 检查systemd/init服务配置

**文件系统安全**:
- 远程检查关键目录权限
- 远程验证系统文件完整性
- 远程检查临时文件配置
- 远程审计日志文件权限
- 检查SUID/SGID文件

**网络安全配置**:
- 远程检查防火墙状态和规则
- 远程验证网络共享配置
- 远程检查SSH配置安全性
- 远程审计网络协议配置
- 检查iptables/firewalld规则

#### 2.1.4 服务器资产管理模块
**功能描述**: 管理远程服务器资产信息
**具体功能**:
- 服务器分组管理（按环境、业务线等）
- 服务器标签和备注管理
- 批量操作支持
- 服务器状态监控
- 连接历史记录

#### 2.1.5 规则管理模块
**功能描述**: 管理检查规则和基线标准
**具体功能**:
- 预置常见安全基线（等保2.0、CIS基线、企业内部基线）
- 针对不同操作系统的规则适配
- 自定义检查脚本和规则
- 规则模板导入导出
- 规则版本管理和更新

#### 2.1.6 报告生成模块
**功能描述**: 生成详细的检查报告
**具体功能**:
- 单服务器和批量检查报告
- 实时检查进度和结果展示
- 风险等级分类和统计
- 多格式报告导出（HTML、PDF、Excel）
- 历史检查记录和趋势分析
- 合规性报告生成

#### 2.1.7 配置管理模块
**功能描述**: 应用程序配置和用户偏好设置
**具体功能**:
- SSH连接参数配置
- 检查项目启用/禁用配置
- 定时检查任务设置
- 报告模板自定义
- 界面主题和语言设置

### 2.2 用户界面设计

#### 2.2.1 主界面
- **服务器概览**: 显示所有管理服务器状态和最近检查结果
- **连接状态面板**: 实时显示SSH连接状态
- **快速检查按钮**: 支持单台或批量服务器检查
- **检查进度显示**: 多任务并行检查进度监控

#### 2.2.2 服务器管理界面
- **服务器列表**: 分组显示管理的服务器
- **连接配置**: SSH连接参数设置
- **批量操作**: 支持批量添加、删除、检查
- **服务器详情**: 显示服务器基本信息和检查历史

#### 2.2.3 检查结果界面
- **多服务器结果对比**: 横向对比多台服务器检查结果
- **详细检查报告**: 分类显示各项检查结果
- **风险评估仪表板**: 可视化风险分布和统计
- **修复建议**: 针对不同操作系统提供修复指导

#### 2.2.4 规则配置界面
- **基线模板选择**: 选择适用的安全基线标准
- **自定义规则编辑**: 支持Shell脚本和PowerShell脚本
- **规则测试**: 在线测试自定义规则
- **规则版本管理**: 规则更新和回滚

#### 2.2.5 报告管理界面
- **报告生成**: 支持单服务器和批量报告
- **历史报告**: 历史检查记录和报告管理
- **趋势分析**: 服务器安全状况变化趋势
- **合规性报告**: 按基线标准生成合规报告

## 3. 非功能需求

### 3.1 性能要求
- 应用启动时间 < 3秒
- 单台服务器完整检查时间 < 5分钟
- 支持同时管理100+台服务器
- 支持并发SSH连接（最多20个并发）
- 内存占用 < 200MB
- 网络超时和重连机制

### 3.2 可用性要求
- 界面简洁直观，无需培训即可使用
- 支持中英文界面切换
- SSH连接失败友好提示
- 提供详细的帮助文档和操作指南
- 错误信息清晰易懂，包含解决建议

### 3.3 安全要求
- SSH连接信息加密存储
- 支持SSH密钥认证
- 不在远程服务器留存检查脚本
- 检查过程只读，不修改系统配置
- 支持审计日志记录
- 敏感信息脱敏处理

### 3.4 兼容性要求
- 支持主流Linux发行版和Windows Server
- 兼容不同SSH服务器版本
- 支持IPv4和IPv6连接
- 向后兼容旧版本配置文件
- 支持不同架构（x64、ARM64）

## 4. 技术架构设计

### 4.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Go后端服务    │    │   远程服务器    │
│  (HTML/CSS/JS)  │◄──►│   (Wails App)   │◄──►│   (SSH连接)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   数据存储      │    │   检查脚本      │
                       │   (SQLite)      │    │   (Shell/PS)    │
                       └─────────────────┘    └─────────────────┘
```

### 4.2 核心模块设计

#### 4.2.1 SSH连接管理器 (SSHManager)
- SSH连接池管理
- 认证管理（密码/密钥）
- 连接状态监控
- 自动重连机制
- 并发连接控制

#### 4.2.2 远程检查引擎 (RemoteCheckEngine)
- 检查脚本生成器
- 远程命令执行器
- 结果解析器
- 错误处理器
- 超时控制

#### 4.2.3 服务器管理器 (ServerManager)
- 服务器资产管理
- 分组和标签管理
- 批量操作支持
- 状态监控
- 配置同步

#### 4.2.4 规则引擎 (RuleEngine)
- 规则模板管理
- 脚本生成器
- 多平台适配
- 规则验证器
- 版本控制

#### 4.2.5 数据管理 (DataManager)
- SQLite数据库操作
- 加密存储管理
- 缓存管理
- 数据导入导出
- 备份恢复

#### 4.2.6 报告生成器 (ReportGenerator)
- 多格式报告生成
- 模板引擎
- 图表生成器
- 数据可视化
- 合规性报告

## 5. 数据库设计

### 5.1 主要数据表

#### 5.1.1 服务器信息表 (servers)
```sql
CREATE TABLE servers (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    host TEXT NOT NULL,
    port INTEGER DEFAULT 22,
    username TEXT NOT NULL,
    auth_type TEXT NOT NULL, -- 'password' or 'key'
    password TEXT, -- 加密存储
    private_key_path TEXT,
    group_name TEXT,
    tags TEXT,
    description TEXT,
    status TEXT DEFAULT 'unknown',
    last_check_time DATETIME,
    created_at DATETIME,
    updated_at DATETIME
);
```

#### 5.1.2 检查规则表 (check_rules)
```sql
CREATE TABLE check_rules (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    severity TEXT,
    os_type TEXT NOT NULL, -- 'linux', 'windows', 'all'
    script_content TEXT, -- Shell或PowerShell脚本
    enabled BOOLEAN DEFAULT 1,
    baseline_type TEXT, -- 'cis', '等保', 'custom'
    version TEXT,
    created_at DATETIME,
    updated_at DATETIME
);
```

#### 5.1.3 检查结果表 (check_results)
```sql
CREATE TABLE check_results (
    id INTEGER PRIMARY KEY,
    session_id TEXT NOT NULL,
    server_id INTEGER,
    rule_id INTEGER,
    status TEXT NOT NULL, -- 'pass', 'fail', 'error', 'skip'
    message TEXT,
    details TEXT,
    execution_time INTEGER, -- 执行时间(毫秒)
    checked_at DATETIME,
    FOREIGN KEY (server_id) REFERENCES servers(id),
    FOREIGN KEY (rule_id) REFERENCES check_rules(id)
);
```

#### 5.1.4 检查会话表 (check_sessions)
```sql
CREATE TABLE check_sessions (
    id TEXT PRIMARY KEY,
    session_type TEXT, -- 'single', 'batch'
    server_ids TEXT, -- JSON数组存储服务器ID列表
    baseline_type TEXT,
    start_time DATETIME,
    end_time DATETIME,
    total_checks INTEGER,
    passed_checks INTEGER,
    failed_checks INTEGER,
    error_checks INTEGER,
    status TEXT, -- 'running', 'completed', 'failed'
    created_by TEXT,
    notes TEXT
);
```

#### 5.1.5 SSH连接日志表 (ssh_logs)
```sql
CREATE TABLE ssh_logs (
    id INTEGER PRIMARY KEY,
    server_id INTEGER,
    action TEXT, -- 'connect', 'disconnect', 'execute'
    status TEXT, -- 'success', 'failed'
    message TEXT,
    duration INTEGER, -- 连接/执行时间(毫秒)
    created_at DATETIME,
    FOREIGN KEY (server_id) REFERENCES servers(id)
);
```

## 6. 开发计划

### 6.1 开发阶段

#### 第一阶段 (2周)
- 项目初始化和基础架构搭建
- Wails项目创建和配置
- 基础UI界面设计
- 数据库设计和创建

#### 第二阶段 (3周)
- 系统信息收集模块开发
- 基础检查规则实现
- 检查引擎核心功能开发
- 基本UI交互实现

#### 第三阶段 (2周)
- 报告生成功能开发
- 配置管理功能实现
- 规则管理界面开发
- 错误处理和日志记录

#### 第四阶段 (1周)
- 跨平台测试和优化
- 性能调优
- 用户界面优化
- 文档编写

### 6.2 交付物
- 可执行程序（Windows/macOS/Linux）
- 用户使用手册
- 技术文档
- 源代码和构建脚本

## 7. 风险评估

### 7.1 技术风险
- **跨平台兼容性**: 不同操作系统API差异
- **性能问题**: 大量检查项目可能影响性能
- **权限问题**: 某些检查需要管理员权限

### 7.2 缓解措施
- 充分的跨平台测试
- 异步执行和进度反馈
- 权限检查和友好提示
- 模块化设计便于维护

## 8. 总结

本基线核查工具设计简洁实用，功能覆盖主要安全检查需求，采用现代化的技术栈确保良好的用户体验和跨平台兼容性。通过模块化设计和清晰的架构，便于后续功能扩展和维护。
